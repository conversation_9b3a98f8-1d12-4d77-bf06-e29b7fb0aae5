from unittest import TestCase
from unittest.mock import patch, MagicMock

from django.conf import settings

from mongo.analytics import get_node_calls, get_node_calls_count, get_request_count, get_flow_logs
from mongo.types import Status

from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    wait_flow_end,
    # REMOVED DANGEROUS IMPORTS:
    # clear_flows_collection,  # DROPS ENTIRE FLOWS COLLECTION!
    # clear_gridfs,           # CLEARS ALL GRIDFS DATA!
)
from symmy.test_utils.model_mixins import (
    SimpleFlowData,
    NestedActionsFlowData,
    SimpleExceptionFlowData,
    ComplexExceptionFlowData,
    SimpleRetryFlowData,
)

RETRY_BACKOFF = 1


class FlowExecutionMixin(CheckTestsAllowedMixin):
    def execute_normal_flow(self, flow_wait_timeout=60):
        flow = self.flow_data.flow
        node_count = self.flow_data.node_count
        supposed_request_count = self.flow_data.get_supposed_request_count()

        mongo_flow_id = flow.execute()
        s = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=flow_wait_timeout)

        self.assertNotEqual(s, "Failed", "Flow failed!")

        self.assertEqual(
            get_node_calls_count(
                mongo_flow_ids=[mongo_flow_id], node_status=Status.success
            ),
            node_count,
            "Successful node call count doesn't match the expected node call count",
        )
        self.assertEqual(
            get_request_count(mongo_flow_ids=[mongo_flow_id]),
            supposed_request_count,
            "Actual request count doesn't match the expected request count",
        )
        return mongo_flow_id

    def execute_exception_flow(self, flow_wait_timeout=60):
        flow = self.flow_data.flow
        exception_node_count = self.flow_data.exception_node_count
        supposed_request_count = self.flow_data.get_supposed_request_count()

        mongo_flow_id = flow.execute(retry_backoff=RETRY_BACKOFF)
        s = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=flow_wait_timeout)
        self.assertEqual(s, "Failed", "Flow did not fail but should have!")

        self.assertEqual(
            get_node_calls_count(
                mongo_flow_ids=[mongo_flow_id], node_status=Status.failed
            ),
            exception_node_count,
            "Failed node call count doesn't match the expected failed node call count",
        )
        self.assertEqual(
            get_request_count(mongo_flow_ids=[mongo_flow_id]),
            supposed_request_count,
            "Actual request count doesn't match the expected request count",
        )

        return mongo_flow_id

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        # REMOVED DANGEROUS CALLS:
        # clear_flows_collection() - DROPS ENTIRE FLOWS COLLECTION FROM PRODUCTION!
        # clear_gridfs() - CLEARS ALL GRIDFS DATA FROM PRODUCTION!
        # These functions are extremely dangerous and should NEVER be called in tests!


class TestSimpleFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()

    def test_simple_flow(self):
        self.execute_normal_flow()


class TestNestedActionsExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = NestedActionsFlowData()

    def test_complex_flow(self):
        self.execute_normal_flow()


class TestSimpleExceptionFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleExceptionFlowData()

    def test_simple_exception_flow(self):
        self.execute_exception_flow()


class TestComplexExceptionFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = ComplexExceptionFlowData()

    def test_complex_exception_flow(self):
        self.execute_exception_flow()


class TestRetryFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleRetryFlowData()

    def test_simple_retry_flow(self):
        mongo_flow_id = self.execute_exception_flow(
            flow_wait_timeout=sum(
                (
                    RETRY_BACKOFF * retry
                    for retry in range(1, settings.CELERY_TASK_MAX_RETRIES + 1)
                )
            )
            * 3
        )
        self.assertEqual(
            get_node_calls(mongo_flow_ids=[mongo_flow_id])["node_calls"][0]["retries"],
            settings.CELERY_TASK_MAX_RETRIES,
        )


class TestFlowConnectionFailure(CheckTestsAllowedMixin, TestCase):
    """Test flow behavior when first node fails to connect."""

    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()

    @patch('flows.models.Node.objects.get')
    def test_flow_fails_when_first_node_cannot_connect(self, mock_node_get):
        """Test that flow is marked as failed when first node fails to connect."""
        # Mock Node.objects.get to raise an exception (simulating connection failure)
        mock_node_get.side_effect = Exception("Could not connect to node")

        flow = self.flow_data.flow

        # Execute the flow - this should fail due to connection error
        mongo_flow_id = flow.execute(retry_backoff=RETRY_BACKOFF)

        # Wait for flow to complete (should be quick since it fails immediately)
        status = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=10)

        # Flow should be marked as failed, not success
        self.assertEqual(status, "Failed", "Flow should be marked as Failed when first node fails to connect")

        # Verify flow log shows failed status
        flow_logs = get_flow_logs(mongo_flow_ids=[mongo_flow_id])
        flow_log = flow_logs["logs"][0]
        self.assertEqual(flow_log["status"]["name"], "Failed")

        # Should have at least one failed node call
        failed_node_count = get_node_calls_count(
            mongo_flow_ids=[mongo_flow_id], node_status=Status.failed
        )
        self.assertGreaterEqual(failed_node_count, 1, "Should have at least one failed node call")

    @patch('flows.tasks.NodeTask.__init__')
    def test_flow_fails_when_node_task_creation_fails(self, mock_node_task_init):
        """Test that flow is marked as failed when NodeTask creation fails."""
        # Mock NodeTask.__init__ to raise an exception
        mock_node_task_init.side_effect = Exception("NodeTask creation failed")

        flow = self.flow_data.flow

        # Execute the flow - this should fail due to NodeTask creation error
        mongo_flow_id = flow.execute(retry_backoff=RETRY_BACKOFF)

        # Wait for flow to complete (should be quick since it fails immediately)
        status = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=10)

        # Flow should be marked as failed, not success
        self.assertEqual(status, "Failed", "Flow should be marked as Failed when NodeTask creation fails")

        # Verify flow log shows failed status
        flow_logs = get_flow_logs(mongo_flow_ids=[mongo_flow_id])
        flow_log = flow_logs["logs"][0]
        self.assertEqual(flow_log["status"]["name"], "Failed")

        # Should have at least one failed node call
        failed_node_count = get_node_calls_count(
            mongo_flow_ids=[mongo_flow_id], node_status=Status.failed
        )
        self.assertGreaterEqual(failed_node_count, 1, "Should have at least one failed node call")


class TestFlowExecutionEagerMode(CheckTestsAllowedMixin, TestCase):
    """Test flow behavior when CELERY_TASK_ALWAYS_EAGER is True (cron-like execution)."""

    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()

    @patch('django.conf.settings.CELERY_TASK_ALWAYS_EAGER', True)
    @patch('flows.models.Node.objects.get')
    def test_flow_fails_correctly_in_eager_mode(self, mock_node_get):
        """Test that flow is marked as failed when first node fails in eager mode (simulating cron execution)."""
        # Mock Node.objects.get to raise an exception (simulating connection failure)
        mock_node_get.side_effect = Exception("Could not connect to node")

        flow = self.flow_data.flow

        # Execute the flow - this should fail due to connection error
        mongo_flow_id = flow.execute(retry_backoff=RETRY_BACKOFF)

        # In eager mode, execution is synchronous, so we don't need to wait
        # Check the flow status directly
        flow_logs = get_flow_logs(mongo_flow_ids=[mongo_flow_id])
        flow_log = flow_logs["logs"][0]

        # Flow should be marked as failed, not success
        self.assertEqual(flow_log["status"]["name"], "Failed",
                        "Flow should be marked as Failed when first node fails in eager mode")

        # Should have at least one failed node call
        failed_node_count = get_node_calls_count(
            mongo_flow_ids=[mongo_flow_id], node_status=Status.failed
        )
        self.assertGreaterEqual(failed_node_count, 1, "Should have at least one failed node call in eager mode")

    @patch('flows.utils.FlowExecutionLock.acquire')
    def test_flow_skipped_when_already_running(self, mock_lock_acquire):
        """Test that flow is marked as skipped when another instance is already running."""
        # Mock lock acquisition to fail (simulating another instance running)
        mock_lock_acquire.return_value = False

        flow = self.flow_data.flow

        # Execute the flow - this should be skipped due to lock failure
        result = flow.execute(retry_backoff=RETRY_BACKOFF)

        # Result should be a dict with error information, not just a mongo_flow_id
        self.assertIsInstance(result, dict, "Result should be a dict when flow is skipped")
        self.assertEqual(result.get("status"), "skipped", "Flow should be marked as skipped")
        self.assertIn("error", result, "Result should contain error message")

        # Check the flow log status
        mongo_flow_id = result.get("mongo_flow_id")
        if mongo_flow_id:
            flow_logs = get_flow_logs(mongo_flow_ids=[mongo_flow_id])
            if flow_logs["logs"]:
                flow_log = flow_logs["logs"][0]
                self.assertEqual(flow_log["status"]["name"], "Skipped",
                               "Flow log should be marked as Skipped when another instance is running")

    @patch('flows.utils.FlowExecutionLock.acquire')
    def test_cron_execution_when_already_running(self, mock_lock_acquire):
        """Test that cron execution (direct task call) behaves correctly when flow is already running."""
        from flows.tasks import execute_flow

        # Mock lock acquisition to fail (simulating another instance running)
        mock_lock_acquire.return_value = False

        flow = self.flow_data.flow

        # Execute the flow directly via task (simulating cron execution)
        result = execute_flow(flow.id, retry_backoff=RETRY_BACKOFF)

        # Result should be a dict with error information when flow is skipped
        self.assertIsInstance(result, dict, "Direct task execution should return dict when flow is skipped")
        self.assertEqual(result.get("status"), "skipped", "Flow should be marked as skipped in direct execution")
        self.assertIn("error", result, "Result should contain error message")

        # Check the flow log status
        mongo_flow_id = result.get("mongo_flow_id")
        if mongo_flow_id:
            flow_logs = get_flow_logs(mongo_flow_ids=[mongo_flow_id])
            if flow_logs["logs"]:
                flow_log = flow_logs["logs"][0]
                self.assertEqual(flow_log["status"]["name"], "Skipped",
                               "Flow log should be marked as Skipped in direct task execution")

    @patch('flows.models.Node.objects.get')
    def test_cron_execution_with_ping_failure(self, mock_node_get):
        """Test that cron execution (direct task call) correctly handles ping failures."""
        from flows.tasks import execute_flow

        # Mock Node.objects.get to raise an exception (simulating ping failure)
        mock_node_get.side_effect = Exception("ping failed")

        flow = self.flow_data.flow

        # Execute the flow directly via task (simulating cron execution)
        result = execute_flow(flow.id, retry_backoff=RETRY_BACKOFF)

        # Result should be a mongo_flow_id string when flow starts but fails
        self.assertIsInstance(result, str, "Direct task execution should return mongo_flow_id when flow starts")

        # Check the flow log status - it should be Failed, not Success
        flow_logs = get_flow_logs(mongo_flow_ids=[result])
        self.assertGreater(len(flow_logs["logs"]), 0, "Should have at least one flow log")

        flow_log = flow_logs["logs"][0]
        self.assertEqual(flow_log["status"]["name"], "Failed",
                        "Flow should be marked as Failed when ping fails in cron execution")

        # Should have at least one failed node call
        failed_node_count = get_node_calls_count(
            mongo_flow_ids=[result], node_status=Status.failed
        )
        self.assertGreaterEqual(failed_node_count, 1, "Should have at least one failed node call in cron execution")
